supabase_deployment
=========
[![Build Status](https://badge.orange-ci.woa.com/lightbox/microservices/supabase_deployment.svg)](http://orange-ci.oa.com/build/log/latest?slug=lightbox/microservices/supabase_deployment)
![](https://metrics.woa.com/git/lightbox/microservices/supabase_deployment/-/latest/ci/status/push)
![](https://metrics.woa.com/git/lightbox/microservices/supabase_deployment/-/latest/ci/pipeline-as-code)
![](https://metrics.woa.com/git/lightbox/microservices/supabase_deployment/-/latest/ci/clone-cost)
![](https://metrics.woa.com/git/lightbox/microservices/supabase_deployment/-/latest/code/repo-config)
![](https://metrics.woa.com/git/lightbox/microservices/supabase_deployment/-/latest/testing/unit/coverage)

The open source Firebase alternative. Supabase gives you a dedicated Postgres database to build your web, mobile, and AI applications.
