"""Contains information and properties pertaining to <PERSON>z package."""
name = "supabase_deployment"
authors = ["Hallong"]
uuid = "b2721da5-3bf8-4eec-bd89-7e2fd1d6dc91"
description = "The open source Firebase alternative. Supabase gives you a dedicated Postgres database to build your web, mobile, and AI applications."
homepage = "https://git.woa.com/lightbox/microservices/supabase_deployment"
tools = []
build_requires = []
private_build_requires = [
    "rez_builder-0",
    "setuptools_scm-1.15",
]
requires = ["python-3.7..4", "lightbox_config-1", "setuptools-41..100"]
# Internal variable used by `rezolve_me` for dev environment.
# Overrides `requires`. Uncomment this only if needed.
# dev_requires = requires + ["pytest-4.6", "pytest_cov-2.10"]
variants = []
tests = {
    "simple_import_test": {"command": 'python -c "import supabase_deployment"'},
    "python-3.7": {
        "command": "pytest --cov=supabase_deployment --pyargs supabase_deployment",
        "requires": ["pytest-4.6", "pytest_cov-2.10", "python-3.7"]
    },
    "python-3.8": {
        "command": "pytest --cov=supabase_deployment --pyargs supabase_deployment",
        "requires": ["pytest-4.6", "pytest_cov-2.10", "python-3.8"]
    },
    "python-3.9": {
        "command": "pytest --cov=supabase_deployment --pyargs supabase_deployment",
        "requires": ["pytest-4.6", "pytest_cov-2.10", "python-3.9"]
    },
    "python-3.10": {
        "command": "pytest --cov=supabase_deployment --pyargs supabase_deployment",
        "requires": ["pytest-7.2", "pytest_cov-4.0", "python-3.10"]
    },
    "python-3.11": {
        "command": "pytest --cov=supabase_deployment --pyargs supabase_deployment",
        "requires": ["pytest-7.2", "pytest_cov-4.0", "python-3.11"]
    },
}


def commands():
    """Set up package."""
    env.PYTHONPATH.prepend("{this.root}/site-packages")  # noqa: F821
