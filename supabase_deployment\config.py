"""Get config information from the global config data."""

# Import third-party modules
from lightbox_config import Configuration
from supabase_deployment.constants import PACKAGE_NAME


def get_config(key):
    """Read config file and return value by key.

    Args:
        key (str): Key to get value from the config file.

    Returns:
        str or dict: Value from key.

    """
    config = Configuration()
    return config.query(PACKAGE_NAME, key)
