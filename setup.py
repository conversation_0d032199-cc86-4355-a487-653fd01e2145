"""Describe our module distribution to Distutils."""

# Import future modules
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

# Import third-party modules
from setuptools import find_packages
from setuptools import setup

setup(
    name="supabase_deployment",
    author="<PERSON><PERSON>",
    author_email="<PERSON><EMAIL>",
    url="https://git.woa.com/lightbox/microservices/supabase_deployment",
    package_dir={"": "."},
    packages=find_packages("."),
    description="The open source Firebase alternative. Supabase gives you a dedicated Postgres database to build your web, mobile, and AI applications.",
    entry_points={},
    classifiers=[
        "Programming Language :: Python",
        "Programming Language :: Python :: 3",
    ],
    use_scm_version=True,
    setup_requires=["setuptools_scm"],
)
